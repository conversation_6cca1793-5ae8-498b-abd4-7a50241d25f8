package cn.hanyi.common.ip.resolver;

import cn.hanyi.common.ip.resolver.platform.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import javax.annotation.PostConstruct;

@Slf4j
@Configuration
public class IpResolverAutoConfiguration implements WebMvcConfigurer {

    @Autowired
    private IpResolverProperties properties;

    @Autowired
    private IpResolverService service;
    
    @PostConstruct
    public void init() {
        log.info("auto config ip-resolver");

        IpResolver resolver = null;
        String platform = properties.getDefaultPlatform();
        switch (platform) {
            case "local":
                IpResolverProperties.LocalConfig localConfig = properties.getLocal();
                if (localConfig != null) {
                    log.info("加载LocalIpResolver");
                    resolver = new LocalIpResolver();

                }
                break;
//            case "ipip":
//                resolver = new IpIpNetResolver();
//                break;
            case "amap":
                IpResolverProperties.AmapConfig amapConfig = properties.getAmap();
                if (amapConfig != null) {
                    log.info("加载AMAP");
                    resolver = new AmapIpResolver(amapConfig);
                }
                break;
            case "baidu":
                IpResolverProperties.BaiDuConfig baiduConfig = properties.getBaidu();
                if (baiduConfig != null) {
                    log.info("加载baidu");
                    resolver = new BaiDuIpResolver(baiduConfig);
                }
                break;
            case "tencent":
                IpResolverProperties.TencentConfig tencentConfig = properties.getTencent();
                if (tencentConfig != null) {
                    log.info("加载tencent");
                    resolver = new TencentIpResolver(tencentConfig);
                }
                break;
            case "huachen":
                IpResolverProperties.HuachenConfig huachenConfig = properties.getHuachen();
                if (huachenConfig != null) {
                    log.info("加载huachen");
                    resolver = new HuachenIpResolver(huachenConfig);
                }
                break;
        }

        if (resolver != null) {
            service.registerResolver(platform, resolver);
        }
    }
}
