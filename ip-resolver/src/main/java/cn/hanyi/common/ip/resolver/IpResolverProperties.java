package cn.hanyi.common.ip.resolver;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;


@Data
@Component
@ConfigurationProperties(prefix = "hanyi.common.ip-resolver")
public class IpResolverProperties {
    /**
     * 默认Platform
     */
    private String defaultPlatform = "local";

    /**
     * local: ip2region
     */
    private LocalConfig local = null;
    
    /**
     * 高德
     */
    private AmapConfig amap = null;

    /**
     * 百度
     */
    private BaiDuConfig baidu = null;

    /**
     * 腾讯
     */
    private TencentConfig tencent = null;

    /**
     * Huachen
     */
    private HuachenConfig huachen = null;

    /**
     * Ip2Region
     */
    @Data
    public static class LocalConfig {
        private String algorithm = "memory";
    }

    /**
     * AMAP 高德
     */
    @Data
    public static class AmapConfig {
        private String url;
        private String apiKey;
        private String apiSecret;
    }

    /**
     * Baidu 百度
     */
    @Data
    public static class BaiDuConfig {
        private String url;
        private String apiKey;
        private String apiSecret;
    }

    /**
     * Tencent 腾讯
     */
    @Data
    public static class TencentConfig {
        private String urlIP;
        private String url;
        private String apiKey;
        private String apiSecret;
    }

    /**
     * Huachen
     */
    @Data
    public static class HuachenConfig {
        private String appCode;
    }
}

