package cn.hanyi.common.ip.resolver.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * 华辰IP解析API响应数据模型
 * 参考TencentApiResultDto格式设计
 */
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
@Builder
public class HuachenApiResultDto {
    @JsonProperty("ret")
    private int ret;

    @JsonProperty("msg")
    private String msg;

    @JsonProperty("data")
    private IpInfoDto data;

    @JsonProperty("log_id")
    private String logId;

    /**
     * IP信息详情内部类
     * 对应API响应中的data字段
     */
    @NoArgsConstructor
    @AllArgsConstructor
    @Getter
    @Setter
    @Builder
    public static class IpInfoDto {
        @JsonProperty("ip")
        private String ip;

        @JsonProperty("long_ip")
        private String longIp;

        @JsonProperty("isp")
        private String isp;

        @JsonProperty("area")
        private String area;

        @JsonProperty("region_id")
        private String regionId;

        @JsonProperty("region")
        private String region;

        @JsonProperty("city_id")
        private String cityId;

        @JsonProperty("city")
        private String city;

        @JsonProperty("country_id")
        private String countryId;

        @JsonProperty("country")
        private String country;
    }
}
