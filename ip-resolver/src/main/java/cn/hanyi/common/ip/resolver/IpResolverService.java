package cn.hanyi.common.ip.resolver;

import cn.hanyi.common.ip.resolver.exception.IpResolverRuntimeException;
import cn.hanyi.common.ip.resolver.platform.IpResolver;
import lombok.Setter;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.apache.commons.net.util.SubnetUtils;
import java.util.HashMap;
import java.util.Map;

import com.google.common.net.InetAddresses;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Service
@Setter
@Slf4j
public class IpResolverService {
    private Map<String, IpResolver> resolverMap = new HashMap<>();

    private static final SubnetUtils.SubnetInfo[] PRIVATE_SUBNETS;

    // 静态块初始化子网信息
    static {
        String[] privateSubnetStrings = {
                "10.0.0.0/8",          // A类私有地址
                "**********/12",       // B类私有地址
                "***********/16",      // C类私有地址
                "*********/8"          // 回环地址
        };

        PRIVATE_SUBNETS = new SubnetUtils.SubnetInfo[privateSubnetStrings.length];
        for (int i = 0; i < privateSubnetStrings.length; i++) {
            SubnetUtils utils = new SubnetUtils(privateSubnetStrings[i]);
            // 关闭严格模式，允许子网掩码不连续（根据实际需求选择）
            utils.setInclusiveHostCount(true);
            PRIVATE_SUBNETS[i] = utils.getInfo();
        }
    }

    @Autowired
    private IpResolverProperties properties;

    public void registerResolver(String name, IpResolver resolver) {
        resolverMap.put(name, resolver);
    }

    public IpResolverService(IpResolverProperties properties) {
        this.properties = properties;
    }

    /**
     * 判断IP地址是否为局域网地址
     * @param ip 要检查的IP地址字符串
     * @return 是局域网地址返回true，否则返回false
     */
    public static boolean isLanIp(String ip) {
        for (SubnetUtils.SubnetInfo subnet : PRIVATE_SUBNETS) {
            if (subnet.isInRange(ip)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 判断是否为IPv4地址
     */
    public static boolean isIPv4(String ip) {
        try {
            return InetAddresses.forString(ip).getAddress().length == 4;
        } catch (IllegalArgumentException e) {
            return false;
        }
    }


    @SneakyThrows
    public RegionInfo resolveIpToRegion(String ip) {
        RegionInfo result = RegionInfo.builder()
                .country("中国")
                .province("未知")
                .city("未知")
                .district("未知")
                .build();
        // 如果不是ipv4或者内网地址直接返回
        if (ip.isEmpty() || !isIPv4(ip) || isLanIp(ip)) {
            return result;
        }

        String[] platforms = properties.getDefaultPlatform().split(",");
        for (int i = 0; i < platforms.length; i++) {
            String platform = platforms[i];
            if (resolverMap.containsKey(platform)) {
                IpResolver resolver = resolverMap.get(platform);
                RegionInfo info = resolver.resolveRegion(ip);
                if (info != null && !info.getCity().equals("未知") && !info.getCity().isEmpty()) {
                    return info;
                }
                log.info("ip：{} platform {} resolved unknown continue", ip, platform);
            }
        }
        return result;
    }

    @SneakyThrows
    public RegionInfo resolveLocationToRegion(Float latitude, Float longitude) {
        String platform = properties.getDefaultPlatform();
        IpResolver resolver = resolverMap.get(platform);
        if (resolver == null) {
            throw new IpResolverRuntimeException("platform not supported");
        }
        return resolver.resolveLocation(latitude,longitude);
    }
}
