package cn.hanyi.common.ip.resolver.platform;

import cn.hanyi.common.ip.resolver.RegionInfo;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.geotools.data.DataStore;
import org.geotools.data.DataStoreFinder;
import org.geotools.data.simple.SimpleFeatureCollection;
import org.geotools.data.simple.SimpleFeatureSource;
import org.geotools.geometry.jts.JTSFactoryFinder;
import org.lionsoul.ip2region.xdb.Searcher;
import org.locationtech.jts.geom.*;
import org.locationtech.jts.index.strtree.STRtree;
import org.opengis.feature.simple.SimpleFeature;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * LocalIpResolver: ip2region 通过实现本地快速IP解析地理位置
 * https://github.com/lionsoul2014/ip2region
 *
 * <AUTHOR>
 */
@Getter
@Slf4j
@Setter
public class LocalIpResolver implements IpResolver {
    private Method method = null;
    private Searcher searcher;

    private SimpleFeatureSource featureSource;
    private GeometryFactory geometryFactory;
    private DataStore dataStore;
    private STRtree spatialIndex;
    private boolean indexBuilt = false;


    /**
     * 初始化查询组件
     */
    private void initializeQueryComponents() throws Exception {

    }


    /**
     * 构建空间索引
     * 将所有要素的几何边界框加入STRtree索引中
     */
    private void buildSpatialIndex() throws Exception {
        if (indexBuilt) {
            return;
        }

        // 1. 初始化几何和过滤器工厂
        geometryFactory = JTSFactoryFinder.getGeometryFactory();

        // 2. 加载Shapefile数据源
        java.net.URL url = getClass().getClassLoader().getResource("SHP/district.shp");
        if (url == null) {
            throw new RuntimeException("Shapefile not found: SHP/district.shp");
        }

        // 使用DataStore参数方式设置字符集，解决中文乱码问题
        Map<String, Object> params = new HashMap<>();
        params.put("url", url);
        params.put("charset", "UTF-8");

        this.dataStore = DataStoreFinder.getDataStore(params);
        if (this.dataStore == null) {
            throw new RuntimeException("无法打开 Shapefile: SHP/district.shp");
        }

        String typeName = this.dataStore.getTypeNames()[0];
        this.featureSource = this.dataStore.getFeatureSource(typeName);

        this.spatialIndex = new STRtree();

        SimpleFeatureCollection features = featureSource.getFeatures();
        try (org.geotools.data.simple.SimpleFeatureIterator iterator = features.features()) {
            while (iterator.hasNext()) {
                SimpleFeature feature = iterator.next();
                Geometry geometry = (Geometry) feature.getDefaultGeometry();
                if (geometry != null) {
                    Envelope envelope = geometry.getEnvelopeInternal();
                    spatialIndex.insert(envelope, feature);
                }
            }
        }

        spatialIndex.build();
        indexBuilt = true;

    }


    /**
     * 根据经纬度查询行政区划信息（使用空间索引优化）
     *
     * @param lng 经度
     * @param lat 纬度
     * @return 包含行政区划信息的SimpleFeature，如果未找到则返回null
     */
    public SimpleFeature queryByCoordinate(double lng, double lat) throws Exception {
        // 确保空间索引已构建
        buildSpatialIndex();

        // 创建查询点
        Point point = geometryFactory.createPoint(new Coordinate(lng, lat));

        // 使用空间索引进行快速预筛选
        List<SimpleFeature> candidates = spatialIndex.query(point.getEnvelopeInternal());

        // 在候选要素中进行精确的几何包含测试
        for (SimpleFeature candidate : candidates) {
            Geometry geometry = (Geometry) candidate.getDefaultGeometry();
            if (geometry != null && geometry.contains(point)) {
                return candidate;
            }
        }

        return null;
    }


    public LocalIpResolver() throws RuntimeException {
        try {
            byte[] bytes = LocalIpResolver.class.getResourceAsStream("/ip2region.xdb").readAllBytes();
            this.searcher = Searcher.newWithBuffer(bytes);
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new RuntimeException("Failed to build db for ip2region: " + ex.getMessage());
        }
    }
    
    @Override
    public RegionInfo resolveRegion(String ip) {
        if (ip == null || !StringUtils.hasText(ip)) {
            return null;
        }

        try {
            String[] result = searcher.search(ip).split("\\|");
            // _城市Id|国家|区域|省份|城市|ISP_
            // 中国|0|广东|深圳|电信
            // 存在未解析到的情况 中国|0|0|0|0 使用未知来代替
//            String[] result = dataBlock.getRegion().split("\\|");
            return RegionInfo.builder()
                    .country(result[0].replace("0", "未知"))
                    .province(result[2].replace("0", "未知").replace("省", ""))
                    .city(result[3].replace("0", "未知").replace("市", ""))
                    .build();
        } catch (InvocationTargetException e) {
            log.error("Failed to resolve region: " + e.getMessage());
            e.printStackTrace();
        } catch (IllegalAccessException e) {
            log.error("Failed to resolve region: " + e.getMessage());
            e.printStackTrace();
        } catch (Exception e) {
        }

        return null;
    }

    @Override
    public RegionInfo resolveLocation(Float latitude, Float longitude) throws IOException {
        RegionInfo info = RegionInfo.builder().country("中国").province("未知").city("未知").district("未知").build();
        try {
            SimpleFeature feature = queryByCoordinate(longitude, latitude);
            if (feature != null) {
                //GPS解析后数据存储的格式：
                //省：除特区、自治区外，其他省份、直辖市存储为如北京、广东，不要带省、市
                //市：除特区、自治区外，直辖市存储为如北京（现在返回的是北京城区）、常规城市存储为如深圳了（现在返回的是深圳市）
                //区：不做处理，按照正常返回存储
                info.setCountry(feature.getAttribute("cn_name").toString());
                info.setProvince(feature.getAttribute("pr_name").toString());
                info.setCity(feature.getAttribute("ct_name").toString());
                info.setDistrict(feature.getAttribute("dt_name").toString());
            }
        } catch (Exception e) {
            log.error("Failed to resolve region: {}", e.getMessage());
        }
        return info;
    }
}
