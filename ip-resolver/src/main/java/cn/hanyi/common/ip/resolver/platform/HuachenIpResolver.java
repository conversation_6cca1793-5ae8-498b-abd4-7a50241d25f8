package cn.hanyi.common.ip.resolver.platform;

import cn.hanyi.common.ip.resolver.IpResolverProperties;
import cn.hanyi.common.ip.resolver.RegionInfo;
import cn.hanyi.common.ip.resolver.dto.HuachenApiResultDto;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.web.client.RestTemplate;

/**
 * 华辰IP解析器
 * 基于华辰API实现的IP地址解析器 阿里云市场
 * https://market.aliyun.com/detail/cmapi021970?spm=5176.29867242_210807074.0.0.44e83e7ee0rmQw
 *
 * <AUTHOR>
 */
public class HuachenIpResolver implements IpResolver {
    private String apiUrl = "https://c2ba.api.huachen.cn/ip?ip=%s";
    private IpResolverProperties.HuachenConfig property;

    public HuachenIpResolver(IpResolverProperties.HuachenConfig property) {
        this.property = property;
    }

    public HuachenIpResolver() {
        if (this.property == null) {
            this.property = new IpResolverProperties.HuachenConfig();
            this.property.setAppCode("");
        }
    }

    @Override
    public RegionInfo resolveRegion(String ip) {
        String appCode = (property.getAppCode() != null && !property.getAppCode().isEmpty())
                ? property.getAppCode()
                : System.getenv("HUACHEN_APP_CODE");

        if (appCode == null || appCode.isEmpty()) {
            return null;
        }
        try {
            RestTemplate restTemplate = new RestTemplate();

            // 创建请求头，设置Authorization
            HttpHeaders headers = new HttpHeaders();
            headers.add("Authorization", "APPCODE " + appCode);

            // 创建HTTP实体
            HttpEntity<String> entity = new HttpEntity<>(headers);

            // 构建请求URL
            String url = String.format(this.apiUrl, ip);

            // 发送GET请求并获取响应
            HuachenApiResultDto response = restTemplate.exchange(
                    url,
                    HttpMethod.GET,
                    entity,
                    HuachenApiResultDto.class
            ).getBody();

            // 处理响应结果，根据API文档，ret=200表示成功
            if (response != null && 200 == response.getRet()) {
                HuachenApiResultDto.IpInfoDto data = response.getData();
                if (data != null) {
                    return RegionInfo.builder()
                            .country(data.getCountry())
                            .province(data.getRegion().replace("省", "").replace("市", ""))
                            .city(data.getCity().replace("市", ""))
                            .district("")
                            .build();
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        // 解析失败时返回默认值
        return RegionInfo.builder()
                .country("中国")
                .province("未知")
                .city("未知")
                .district("未知")
                .build();
    }

    @Override
    public RegionInfo resolveLocation(Float latitude, Float longitude) {
        // 华辰API未提供经纬度解析功能，返回默认值
        return RegionInfo.builder()
                .country("中国")
                .province("未知")
                .city("未知")
                .district("未知")
                .build();
    }
}
