package cn.hanyi.common.ip.test;

import cn.hanyi.common.ip.resolver.IpResolverProperties;
import cn.hanyi.common.ip.resolver.IpResolverService;
import cn.hanyi.common.ip.resolver.RegionInfo;
import cn.hanyi.common.ip.resolver.platform.HuachenIpResolver;
import cn.hanyi.common.ip.resolver.platform.LocalIpResolver;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

/**
 * The class description
 *
 * <AUTHOR>
 */
public class IpResolverServiceTest {

    private LocalIpResolver ipResolver = new LocalIpResolver();

    @Test
    public void testOne() {
        RegionInfo regionInfo = ipResolver.resolveRegion("*************");
    }

    @Test
    public void testLanIp(){
        // 内网地址
        Assertions.assertTrue(IpResolverService.isLanIp("***********"));        Assertions.assertTrue(IpResolverService.isLanIp("***********"));
        Assertions.assertTrue(IpResolverService.isLanIp("127.0.0.1"));
        Assertions.assertTrue(IpResolverService.isLanIp("********"));

        // 外网地址
        Assertions.assertFalse(IpResolverService.isLanIp("**************"));
    }

    @Test
    public void testIp4(){
        // 合理地址
        Assertions.assertTrue(IpResolverService.isIPv4("***********"));        Assertions.assertTrue(IpResolverService.isLanIp("***********"));
        Assertions.assertTrue(IpResolverService.isIPv4("********"));
        Assertions.assertTrue(IpResolverService.isIPv4("**************"));

        // 无效ip4地址
        Assertions.assertFalse(IpResolverService.isIPv4("hello"));
        Assertions.assertFalse(IpResolverService.isIPv4("2001:db8::1"));
        Assertions.assertFalse(IpResolverService.isIPv4("2001:0db8:85a3:0000:0000:8a2e:0370:7334"));
    }

    @Test
    public void testChain(){
        HuachenIpResolver huachenIpResolver = new HuachenIpResolver();
        IpResolverProperties properties = new IpResolverProperties();
        properties.setDefaultPlatform("local,huachen");
        IpResolverService service = new IpResolverService(properties);
        service.registerResolver("local", ipResolver);
        service.registerResolver("huachen", huachenIpResolver);

        RegionInfo regionInfo = service.resolveIpToRegion("**************");
        System.out.println(regionInfo);
    }


    @Test
    public void testHuachenOne(){
        HuachenIpResolver huachenIpResolver = new HuachenIpResolver();
        RegionInfo regionInfo = huachenIpResolver.resolveRegion("**************");
        System.out.println(regionInfo);
    }

}
