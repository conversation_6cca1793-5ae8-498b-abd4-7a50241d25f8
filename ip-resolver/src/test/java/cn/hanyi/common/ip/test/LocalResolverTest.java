package cn.hanyi.common.ip.test;

import cn.hanyi.common.ip.resolver.IpResolverProperties;
import cn.hanyi.common.ip.resolver.RegionInfo;
import cn.hanyi.common.ip.resolver.platform.AmapIpResolver;
import cn.hanyi.common.ip.resolver.platform.BaiDuIpResolver;
import cn.hanyi.common.ip.resolver.platform.HuachenIpResolver;
import cn.hanyi.common.ip.resolver.platform.LocalIpResolver;
import lombok.Data;
import lombok.SneakyThrows;
import org.junit.jupiter.api.Test;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.*;

/**
 * The class description
 *
 * <AUTHOR>
 */
public class LocalResolverTest {

    private LocalIpResolver ipResolver = new LocalIpResolver();
    private AmapIpResolver amapIpResolver = new AmapIpResolver();
    private BaiDuIpResolver baiDuIpResolver = new BaiDuIpResolver();

    private List<IpTestData> cases = loadTest();

    @Data
    public class IpTestData {
        private String country;
        private String ip;
        private String province;
        private String city;
    }


    /**
     *
     */
    @SneakyThrows
    private List<IpTestData> loadTest() {
        InputStream stream = LocalResolverTest.class.getResourceAsStream("/ip_test.csv");
        List<IpTestData> tests = new ArrayList<>();
        try (BufferedReader br = new BufferedReader(new InputStreamReader(stream))) {
            String line;
            while ((line = br.readLine()) != null) {
                String[] values = line.split(",");
                String ip = values[0].replace("\"", "");
                String city = values[1].replace("\"", "");
                String province = values[2].replace("\"", "");
                IpTestData item = new IpTestData();
                item.setCity(city);
                item.setIp(ip);
                item.setProvince(province);
                tests.add(item);
            }
        }
        return tests;
    }

    @Test
    public void testOne() {
        RegionInfo regionInfo = ipResolver.resolveRegion("*************");
    }

    @Test
    public void testipIpNetOne() {
//        RegionInfo regionInfo = ipIpNetResolver.resolveRegion("*************");
//        System.out.println(regionInfo);
    }

    @Test
    public void testAmapOne(){
        RegionInfo regionInfo = amapIpResolver.resolveRegion("**************");
        System.out.println(regionInfo);
    }

    @Test
    public void testBaiDuOne(){
        RegionInfo regionInfo = baiDuIpResolver.resolveRegion("**************");
        System.out.println(regionInfo);
    }

    @Test
    public void testHuachenOne(){
        HuachenIpResolver huachenIpResolver = new HuachenIpResolver();
        RegionInfo regionInfo = huachenIpResolver.resolveRegion("**************");
        System.out.println(regionInfo);
    }

    @Test
    public void testAll() {
        Instant start = Instant.now();
        int total = 0;
        int successProvince = 0;
        int successCity = 0;
        for (int i = 0; i < cases.size(); i++) {
            IpTestData item = cases.get(i);
            RegionInfo regionInfo = ipResolver.resolveRegion(item.getIp());
            if (!regionInfo.getCity().equals("未知")) {
                successCity += 1;
            }
            if (!regionInfo.getProvince().equals("未知")) {
                successProvince += 1;
            }
            if (!regionInfo.getProvince().equals(item.getProvince())) {
                System.out.println("IP " + item.getIp() + " previous " + item.getProvince() + " changed to " + regionInfo.getProvince());
            }
            total += 1;
        }
        Instant end = Instant.now();
        System.out.println("总计 " + total);
        System.out.println("省识别 " + successProvince);
        System.out.println("市识别 " + successCity);
        System.out.println("省识别率 " + Math.round(100 * successProvince / total) + "%");
        System.out.println("市识别率 " + Math.round(100 * successCity / total) + "%");
        System.out.println("耗时 " + ChronoUnit.MILLIS.between(start, end) + "ms");
    }
}
